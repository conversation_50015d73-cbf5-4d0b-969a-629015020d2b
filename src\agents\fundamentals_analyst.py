from langchain_core.messages import HumanMessage
from src.graph.state import Agent<PERSON><PERSON>, show_agent_reasoning
from src.utils.progress import progress
from src.tools.api import get_financial_metrics, search_line_items, get_insider_trades
import pandas as pd
import numpy as np
import json


##### Fundamentals Analyst Agent #####
def fundamentals_analyst_agent(state: AgentState):
    """Analyzes comprehensive fundamental data to generate trading signals for multiple tickers."""
    data = state["data"]
    end_date = data["end_date"]
    tickers = data["tickers"]

    # Initialize fundamentals analysis for each ticker
    fundamentals_analysis = {}

    for ticker in tickers:
        progress.update_status("fundamentals_analyst_agent", ticker, "Fetching financial metrics")

        # Get comprehensive financial metrics
        financial_metrics = get_financial_metrics(
            ticker=ticker,
            end_date=end_date,
            period="ttm",
            limit=10,
            agent_name="fundamentals_analyst_agent",
        )

        # Get additional line items for detailed analysis
        line_items = search_line_items(
            ticker=ticker,
            line_items=["total_revenue", "net_income", "total_assets", "total_debt", "free_cash_flow"],
            end_date=end_date,
            period="ttm",
            limit=5,
            agent_name="fundamentals_analyst_agent",
        )

        if not financial_metrics:
            progress.update_status("fundamentals_analyst_agent", ticker, "Failed: No financial metrics found")
            continue

        # Pull the most recent financial metrics
        metrics = financial_metrics[0]

        # Initialize signals list for different fundamental aspects
        signals = []
        reasoning = {}

        progress.update_status("fundamentals_analyst_agent", ticker, "Analyzing profitability metrics")
        
        # 1. Enhanced Profitability Analysis
        return_on_equity = metrics.return_on_equity
        return_on_assets = metrics.return_on_assets
        net_margin = metrics.net_margin
        operating_margin = metrics.operating_margin
        gross_margin = metrics.gross_margin

        profitability_score = 0
        profitability_details = []
        
        # ROE analysis
        if return_on_equity and return_on_equity > 0.15:
            profitability_score += 1
            profitability_details.append(f"Strong ROE: {return_on_equity:.2%}")
        elif return_on_equity and return_on_equity < 0.05:
            profitability_score -= 1
            profitability_details.append(f"Weak ROE: {return_on_equity:.2%}")
        
        # ROA analysis
        if return_on_assets and return_on_assets > 0.10:
            profitability_score += 1
            profitability_details.append(f"Strong ROA: {return_on_assets:.2%}")
        elif return_on_assets and return_on_assets < 0.03:
            profitability_score -= 1
            profitability_details.append(f"Weak ROA: {return_on_assets:.2%}")
        
        # Margin analysis
        if net_margin and net_margin > 0.15:
            profitability_score += 1
            profitability_details.append(f"High Net Margin: {net_margin:.2%}")
        elif net_margin and net_margin < 0.05:
            profitability_score -= 1
            profitability_details.append(f"Low Net Margin: {net_margin:.2%}")

        signals.append("bullish" if profitability_score >= 2 else "bearish" if profitability_score <= -2 else "neutral")
        reasoning["profitability_signal"] = {
            "signal": signals[0],
            "details": "; ".join(profitability_details) if profitability_details else "Limited profitability data",
        }

        progress.update_status("fundamentals_analyst_agent", ticker, "Analyzing growth metrics")
        
        # 2. Enhanced Growth Analysis
        revenue_growth = metrics.revenue_growth
        earnings_growth = metrics.earnings_growth
        book_value_growth = metrics.book_value_growth

        growth_score = 0
        growth_details = []
        
        if revenue_growth and revenue_growth > 0.10:
            growth_score += 1
            growth_details.append(f"Strong Revenue Growth: {revenue_growth:.2%}")
        elif revenue_growth and revenue_growth < 0:
            growth_score -= 1
            growth_details.append(f"Declining Revenue: {revenue_growth:.2%}")
        
        if earnings_growth and earnings_growth > 0.15:
            growth_score += 1
            growth_details.append(f"Strong Earnings Growth: {earnings_growth:.2%}")
        elif earnings_growth and earnings_growth < -0.10:
            growth_score -= 1
            growth_details.append(f"Declining Earnings: {earnings_growth:.2%}")
        
        if book_value_growth and book_value_growth > 0.10:
            growth_score += 1
            growth_details.append(f"Growing Book Value: {book_value_growth:.2%}")

        signals.append("bullish" if growth_score >= 2 else "bearish" if growth_score <= -1 else "neutral")
        reasoning["growth_signal"] = {
            "signal": signals[1],
            "details": "; ".join(growth_details) if growth_details else "Limited growth data",
        }

        progress.update_status("fundamentals_analyst_agent", ticker, "Analyzing financial health")
        
        # 3. Enhanced Financial Health Analysis
        current_ratio = metrics.current_ratio
        debt_to_equity = metrics.debt_to_equity
        interest_coverage = metrics.interest_coverage_ratio
        free_cash_flow_per_share = metrics.free_cash_flow_per_share

        health_score = 0
        health_details = []
        
        # Liquidity analysis
        if current_ratio and current_ratio > 2.0:
            health_score += 1
            health_details.append(f"Strong Liquidity: {current_ratio:.2f}")
        elif current_ratio and current_ratio < 1.0:
            health_score -= 1
            health_details.append(f"Weak Liquidity: {current_ratio:.2f}")
        
        # Debt analysis
        if debt_to_equity and debt_to_equity < 0.3:
            health_score += 1
            health_details.append(f"Low Debt: {debt_to_equity:.2f}")
        elif debt_to_equity and debt_to_equity > 1.0:
            health_score -= 1
            health_details.append(f"High Debt: {debt_to_equity:.2f}")
        
        # Interest coverage
        if interest_coverage and interest_coverage > 5.0:
            health_score += 1
            health_details.append(f"Strong Interest Coverage: {interest_coverage:.2f}")
        elif interest_coverage and interest_coverage < 2.0:
            health_score -= 1
            health_details.append(f"Weak Interest Coverage: {interest_coverage:.2f}")
        
        # Free cash flow
        if free_cash_flow_per_share and free_cash_flow_per_share > 0:
            health_score += 1
            health_details.append(f"Positive FCF: ${free_cash_flow_per_share:.2f}")

        signals.append("bullish" if health_score >= 2 else "bearish" if health_score <= -2 else "neutral")
        reasoning["financial_health_signal"] = {
            "signal": signals[2],
            "details": "; ".join(health_details) if health_details else "Limited financial health data",
        }

        progress.update_status("fundamentals_analyst_agent", ticker, "Analyzing valuation metrics")
        
        # 4. Enhanced Valuation Analysis
        pe_ratio = metrics.price_to_earnings_ratio
        pb_ratio = metrics.price_to_book_ratio
        ps_ratio = metrics.price_to_sales_ratio
        peg_ratio = metrics.peg_ratio
        ev_ebitda = metrics.enterprise_value_to_ebitda

        valuation_score = 0
        valuation_details = []
        
        # P/E analysis
        if pe_ratio and 10 <= pe_ratio <= 20:
            valuation_score += 1
            valuation_details.append(f"Reasonable P/E: {pe_ratio:.2f}")
        elif pe_ratio and pe_ratio > 30:
            valuation_score -= 1
            valuation_details.append(f"High P/E: {pe_ratio:.2f}")
        
        # P/B analysis
        if pb_ratio and pb_ratio < 2.0:
            valuation_score += 1
            valuation_details.append(f"Low P/B: {pb_ratio:.2f}")
        elif pb_ratio and pb_ratio > 5.0:
            valuation_score -= 1
            valuation_details.append(f"High P/B: {pb_ratio:.2f}")
        
        # PEG analysis
        if peg_ratio and peg_ratio < 1.0:
            valuation_score += 1
            valuation_details.append(f"Attractive PEG: {peg_ratio:.2f}")
        elif peg_ratio and peg_ratio > 2.0:
            valuation_score -= 1
            valuation_details.append(f"High PEG: {peg_ratio:.2f}")

        signals.append("bullish" if valuation_score >= 1 else "bearish" if valuation_score <= -2 else "neutral")
        reasoning["valuation_signal"] = {
            "signal": signals[3],
            "details": "; ".join(valuation_details) if valuation_details else "Limited valuation data",
        }

        progress.update_status("fundamentals_analyst_agent", ticker, "Analyzing insider activity")
        
        # 5. Insider Activity Analysis
        insider_trades = get_insider_trades(
            ticker=ticker,
            end_date=end_date,
            limit=10,
            agent_name="fundamentals_analyst_agent",
        )
        
        if insider_trades:
            insider_score = 0
            buy_transactions = 0
            sell_transactions = 0
            
            for trade in insider_trades:
                if hasattr(trade, 'transaction_shares') and trade.transaction_shares:
                    if trade.transaction_shares > 0:
                        buy_transactions += 1
                        insider_score += 1
                    else:
                        sell_transactions += 1
                        insider_score -= 1
            
            if insider_score > 0:
                insider_signal = "bullish"
            elif insider_score < 0:
                insider_signal = "bearish"
            else:
                insider_signal = "neutral"
                
            insider_details = f"Buys: {buy_transactions}, Sells: {sell_transactions}"
        else:
            insider_signal = "neutral"
            insider_details = "No insider trading data"
        
        signals.append(insider_signal)
        reasoning["insider_signal"] = {
            "signal": insider_signal,
            "details": insider_details,
        }

        progress.update_status("fundamentals_analyst_agent", ticker, "Calculating final signal")
        
        # Determine overall signal with weighted approach
        # Give more weight to profitability and financial health
        weighted_signals = []
        weighted_signals.extend([signals[0]] * 2)  # Profitability (double weight)
        weighted_signals.extend([signals[2]] * 2)  # Financial health (double weight)
        weighted_signals.append(signals[1])  # Growth
        weighted_signals.append(signals[3])  # Valuation
        weighted_signals.append(signals[4])  # Insider activity
        
        bullish_signals = weighted_signals.count("bullish")
        bearish_signals = weighted_signals.count("bearish")

        if bullish_signals > bearish_signals:
            overall_signal = "bullish"
        elif bearish_signals > bullish_signals:
            overall_signal = "bearish"
        else:
            overall_signal = "neutral"

        # Calculate confidence level
        total_weighted_signals = len(weighted_signals)
        confidence = round(max(bullish_signals, bearish_signals) / total_weighted_signals, 2) * 100

        fundamentals_analysis[ticker] = {
            "signal": overall_signal,
            "confidence": confidence,
            "reasoning": reasoning,
        }

        progress.update_status("fundamentals_analyst_agent", ticker, "Done")

    # Create the fundamentals analysis message
    message = HumanMessage(
        content=json.dumps(fundamentals_analysis),
        name="fundamentals_analyst_agent",
    )

    # Print the reasoning if the flag is set
    if state["metadata"]["show_reasoning"]:
        show_agent_reasoning(fundamentals_analysis, "Fundamentals Analysis Agent")

    # Add the signal to the analyst_signals list
    state["data"]["analyst_signals"]["fundamentals_analyst_agent"] = fundamentals_analysis

    progress.update_status("fundamentals_analyst_agent", None, "Done")
    
    return {
        "messages": [message],
        "data": data,
    }
