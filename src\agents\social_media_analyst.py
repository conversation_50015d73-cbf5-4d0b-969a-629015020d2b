from langchain_core.messages import HumanMessage
from src.graph.state import Agent<PERSON><PERSON>, show_agent_reasoning
from src.utils.progress import progress
from src.tools.api import get_company_news, get_insider_trades
import pandas as pd
import numpy as np
import json
from datetime import datetime, timedelta


##### Social Media Analyst Agent #####
def social_media_analyst_agent(state: AgentState):
    """Analyzes social media sentiment and public perception to generate trading signals for multiple tickers."""
    data = state["data"]
    end_date = data["end_date"]
    tickers = data["tickers"]

    # Initialize social media analysis for each ticker
    social_analysis = {}

    for ticker in tickers:
        progress.update_status("social_media_analyst_agent", ticker, "Fetching social sentiment data")

        # Get company news as proxy for social sentiment (since we don't have direct social media access)
        company_news = get_company_news(
            ticker=ticker,
            end_date=end_date,
            limit=50,
            agent_name="social_media_analyst_agent",
        )

        # Get insider trades as additional social sentiment indicator
        insider_trades = get_insider_trades(
            ticker=ticker,
            end_date=end_date,
            limit=20,
            agent_name="social_media_analyst_agent",
        )

        if not company_news and not insider_trades:
            progress.update_status("social_media_analyst_agent", ticker, "Failed: No sentiment data found")
            continue

        # Initialize signals list for different social aspects
        signals = []
        reasoning = {}

        progress.update_status("social_media_analyst_agent", ticker, "Analyzing public sentiment")
        
        # 1. Public Sentiment Analysis (based on news sentiment as proxy)
        if company_news:
            sentiment_scores = []
            for news in company_news:
                if hasattr(news, 'sentiment') and news.sentiment:
                    if news.sentiment.lower() == 'positive':
                        sentiment_scores.append(1)
                    elif news.sentiment.lower() == 'negative':
                        sentiment_scores.append(-1)
                    else:
                        sentiment_scores.append(0)
                else:
                    sentiment_scores.append(0)
            
            if len(sentiment_scores) > 0:
                avg_sentiment = np.mean(sentiment_scores)
                sentiment_strength = abs(avg_sentiment)
                
                if avg_sentiment > 0.15:
                    public_sentiment = "bullish"
                elif avg_sentiment < -0.15:
                    public_sentiment = "bearish"
                else:
                    public_sentiment = "neutral"
            else:
                public_sentiment = "neutral"
                avg_sentiment = 0
                sentiment_strength = 0
        else:
            public_sentiment = "neutral"
            avg_sentiment = 0
            sentiment_strength = 0
            
        signals.append(public_sentiment)
        reasoning["public_sentiment"] = {
            "signal": public_sentiment,
            "details": f"Avg Sentiment: {avg_sentiment:.2f}, Strength: {sentiment_strength:.2f}",
        }

        progress.update_status("social_media_analyst_agent", ticker, "Analyzing insider activity")
        
        # 2. Insider Activity Analysis (as social sentiment indicator)
        if insider_trades:
            insider_signals = []
            total_shares = 0
            buy_shares = 0
            sell_shares = 0
            
            for trade in insider_trades:
                if hasattr(trade, 'transaction_shares') and trade.transaction_shares:
                    shares = trade.transaction_shares
                    total_shares += abs(shares)
                    
                    if shares > 0:  # Buy
                        insider_signals.append("bullish")
                        buy_shares += shares
                    elif shares < 0:  # Sell
                        insider_signals.append("bearish")
                        sell_shares += abs(shares)
            
            if len(insider_signals) > 0:
                bullish_insider = insider_signals.count("bullish")
                bearish_insider = insider_signals.count("bearish")
                
                if bullish_insider > bearish_insider:
                    insider_sentiment = "bullish"
                elif bearish_insider > bullish_insider:
                    insider_sentiment = "bearish"
                else:
                    insider_sentiment = "neutral"
            else:
                insider_sentiment = "neutral"
                buy_shares = 0
                sell_shares = 0
        else:
            insider_sentiment = "neutral"
            buy_shares = 0
            sell_shares = 0
            
        signals.append(insider_sentiment)
        reasoning["insider_sentiment"] = {
            "signal": insider_sentiment,
            "details": f"Buy Shares: {buy_shares:,}, Sell Shares: {sell_shares:,}",
        }

        progress.update_status("social_media_analyst_agent", ticker, "Analyzing attention trends")
        
        # 3. Attention/Buzz Analysis (based on news frequency)
        if company_news:
            end_datetime = datetime.strptime(end_date, "%Y-%m-%d")
            recent_threshold = end_datetime - timedelta(days=7)
            
            recent_news_count = 0
            total_news_count = len(company_news)
            
            for news in company_news:
                if hasattr(news, 'date') and news.date:
                    try:
                        news_date = datetime.strptime(news.date, "%Y-%m-%d")
                        if news_date >= recent_threshold:
                            recent_news_count += 1
                    except:
                        pass
            
            # Calculate attention level
            attention_ratio = recent_news_count / max(total_news_count, 1)
            
            if attention_ratio > 0.4:  # High attention
                if public_sentiment == "bullish":
                    attention_signal = "bullish"
                elif public_sentiment == "bearish":
                    attention_signal = "bearish"
                else:
                    attention_signal = "neutral"
            elif attention_ratio < 0.1:  # Low attention
                attention_signal = "neutral"
            else:
                attention_signal = "neutral"
        else:
            attention_signal = "neutral"
            attention_ratio = 0
            
        signals.append(attention_signal)
        reasoning["attention_signal"] = {
            "signal": attention_signal,
            "details": f"Recent Attention Ratio: {attention_ratio:.2f}",
        }

        progress.update_status("social_media_analyst_agent", ticker, "Analyzing sentiment momentum")
        
        # 4. Sentiment Momentum Analysis
        if company_news and len(company_news) >= 10:
            # Split news into recent and older
            sorted_news = sorted(company_news, key=lambda x: x.date if hasattr(x, 'date') and x.date else '1900-01-01', reverse=True)
            recent_half = sorted_news[:len(sorted_news)//2]
            older_half = sorted_news[len(sorted_news)//2:]
            
            # Calculate sentiment for each half
            recent_sentiment = []
            older_sentiment = []
            
            for news in recent_half:
                if hasattr(news, 'sentiment') and news.sentiment:
                    if news.sentiment.lower() == 'positive':
                        recent_sentiment.append(1)
                    elif news.sentiment.lower() == 'negative':
                        recent_sentiment.append(-1)
                    else:
                        recent_sentiment.append(0)
            
            for news in older_half:
                if hasattr(news, 'sentiment') and news.sentiment:
                    if news.sentiment.lower() == 'positive':
                        older_sentiment.append(1)
                    elif news.sentiment.lower() == 'negative':
                        older_sentiment.append(-1)
                    else:
                        older_sentiment.append(0)
            
            if len(recent_sentiment) > 0 and len(older_sentiment) > 0:
                recent_avg = np.mean(recent_sentiment)
                older_avg = np.mean(older_sentiment)
                momentum = recent_avg - older_avg
                
                if momentum > 0.2:
                    momentum_signal = "bullish"
                elif momentum < -0.2:
                    momentum_signal = "bearish"
                else:
                    momentum_signal = "neutral"
            else:
                momentum_signal = "neutral"
                momentum = 0
        else:
            momentum_signal = "neutral"
            momentum = 0
            
        signals.append(momentum_signal)
        reasoning["momentum_signal"] = {
            "signal": momentum_signal,
            "details": f"Sentiment Momentum: {momentum:.2f}",
        }

        progress.update_status("social_media_analyst_agent", ticker, "Calculating final signal")
        
        # Determine overall signal with weighted approach
        # Give more weight to public sentiment and insider activity
        weighted_signals = []
        weighted_signals.extend([public_sentiment] * 2)  # Double weight
        weighted_signals.extend([insider_sentiment] * 2)  # Double weight
        weighted_signals.append(attention_signal)
        weighted_signals.append(momentum_signal)
        
        bullish_signals = weighted_signals.count("bullish")
        bearish_signals = weighted_signals.count("bearish")

        if bullish_signals > bearish_signals:
            overall_signal = "bullish"
        elif bearish_signals > bullish_signals:
            overall_signal = "bearish"
        else:
            overall_signal = "neutral"

        # Calculate confidence level
        total_weighted_signals = len(weighted_signals)
        confidence = round(max(bullish_signals, bearish_signals) / total_weighted_signals, 2) * 100

        social_analysis[ticker] = {
            "signal": overall_signal,
            "confidence": confidence,
            "reasoning": reasoning,
        }

        progress.update_status("social_media_analyst_agent", ticker, "Done")

    # Create the social media analysis message
    message = HumanMessage(
        content=json.dumps(social_analysis),
        name="social_media_analyst_agent",
    )

    # Print the reasoning if the flag is set
    if state["metadata"]["show_reasoning"]:
        show_agent_reasoning(social_analysis, "Social Media Analysis Agent")

    # Add the signal to the analyst_signals list
    state["data"]["analyst_signals"]["social_media_analyst_agent"] = social_analysis

    progress.update_status("social_media_analyst_agent", None, "Done")
    
    return {
        "messages": [message],
        "data": data,
    }
