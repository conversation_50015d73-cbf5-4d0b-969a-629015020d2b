#!/usr/bin/env python3
"""
测试脚本：验证TradingAgents分析师集成是否正常工作
"""

import sys
import os
from datetime import datetime, timedelta

# 将项目根目录添加到 Python 路径
project_root = os.path.abspath(os.path.dirname(__file__))
sys.path.insert(0, project_root)

from src.main import run_hedge_fund
from src.utils.analysts import ANALYST_CONFIG


def test_tradingagents_integration():
    """测试TradingAgents分析师集成"""
    print("=" * 60)
    print("测试TradingAgents分析师集成")
    print("=" * 60)
    
    # 检查TradingAgents分析师是否已添加到配置中
    tradingagents_analysts = [key for key in ANALYST_CONFIG.keys() if 'tradingagents' in key]
    print(f"发现 {len(tradingagents_analysts)} 个TradingAgents分析师:")
    for analyst in tradingagents_analysts:
        print(f"  - {ANALYST_CONFIG[analyst]['display_name']}")
    
    # 设置测试参数
    tickers = ["AAPL"]  # 使用单个股票进行测试
    end_date = datetime.now().strftime("%Y-%m-%d")
    start_date = (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d")  # 一周前
    
    # 初始化投资组合
    portfolio = {
        "cash": 100000.0,
        "margin_requirement": 0.0,
        "margin_used": 0.0,
        "positions": {
            "AAPL": {
                "long": 0,
                "short": 0,
                "long_cost_basis": 0.0,
                "short_cost_basis": 0.0,
                "short_margin_used": 0.0,
            }
        },
        "realized_gains": {
            "AAPL": {
                "long": 0.0,
                "short": 0.0,
            }
        },
    }
    
    print(f"\n测试参数:")
    print(f"  股票代码: {tickers}")
    print(f"  开始日期: {start_date}")
    print(f"  结束日期: {end_date}")
    print(f"  初始资金: ${portfolio['cash']:,.2f}")
    
    # 测试只使用一个TradingAgents分析师
    test_analyst = "tradingagents_fundamentals_analyst"
    print(f"\n测试分析师: {ANALYST_CONFIG[test_analyst]['display_name']}")
    
    try:
        print("\n开始运行对冲基金系统...")
        result = run_hedge_fund(
            tickers=tickers,
            start_date=start_date,
            end_date=end_date,
            portfolio=portfolio,
            show_reasoning=True,  # 显示推理过程
            selected_analysts=[test_analyst],
            model_name="gpt-4o-mini",  # 使用较便宜的模型进行测试
            model_provider="OpenAI",
        )
        
        print("\n测试结果:")
        print("=" * 40)
        
        # 检查返回结果
        if "decisions" in result:
            print("✅ 投资组合决策生成成功")
            decisions = result["decisions"]
            for ticker, decision in decisions.items():
                print(f"  {ticker}: {decision}")
        else:
            print("❌ 未找到投资组合决策")
        
        if "analyst_signals" in result:
            print("✅ 分析师信号生成成功")
            signals = result["analyst_signals"]
            for analyst_name, analyst_signals in signals.items():
                print(f"  {analyst_name}: {len(analyst_signals)} 个信号")
                if 'tradingagents' in analyst_name:
                    print(f"    TradingAgents分析师信号详情:")
                    for ticker, signal in analyst_signals.items():
                        print(f"      {ticker}: {signal.get('signal', 'N/A')} (置信度: {signal.get('confidence', 'N/A')}%)")
        else:
            print("❌ 未找到分析师信号")
        
        print("\n✅ TradingAgents集成测试成功完成!")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_tradingagents_integration()
    if success:
        print("\n🎉 所有测试通过!")
        sys.exit(0)
    else:
        print("\n💥 测试失败!")
        sys.exit(1)
