"""
适配器模块，用于将tradingagents系统中的分析师代理集成到当前回测系统中。
这些适配器函数将tradingagents的代理接口转换为与当前系统兼容的格式。
"""

import sys
import os
from langchain_core.messages import HumanMessage
from src.graph.state import AgentState, show_agent_reasoning
from src.utils.progress import progress
import json

# 将tradingagents目录添加到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
tradingagents_path = os.path.join(project_root, 'tradingagents')
sys.path.insert(0, tradingagents_path)

# 导入tradingagents的分析师和工具
from tradingagents.agents.analysts.fundamentals_analyst import create_fundamentals_analyst
from tradingagents.agents.analysts.market_analyst import create_market_analyst
from tradingagents.agents.analysts.news_analyst import create_news_analyst
from tradingagents.agents.analysts.social_media_analyst import create_social_media_analyst
from tradingagents.agents.utils.agent_utils import Toolkit
from src.llm.models import get_model, ModelProvider


class TradingAgentsToolkitAdapter:
    """
    适配器类，用于将tradingagents的Toolkit适配到当前系统。
    """
    
    def __init__(self, model_name: str, model_provider: str):
        self.model_name = model_name
        self.model_provider = model_provider
        
        # 创建LLM实例
        # 将字符串provider转换为ModelProvider枚举
        if isinstance(model_provider, str):
            model_provider = ModelProvider(model_provider)
        self.llm = get_model(model_name, model_provider)
        
        # 创建tradingagents的toolkit
        # 设置为使用离线工具，因为当前系统有自己的数据获取机制
        config = {
            "online_tools": False,
            "llm_provider": "openai",  # 保持兼容性
        }
        self.toolkit = Toolkit(config)


def _convert_tradingagents_state_to_current(ticker: str, start_date: str, end_date: str):
    """
    将当前系统的状态转换为tradingagents系统期望的状态格式。
    """
    return {
        "trade_date": end_date,
        "company_of_interest": ticker,
        "messages": [HumanMessage(content="Make trading decisions based on the provided data.")],
    }


def _extract_signal_from_tradingagents_output(output: dict, agent_name: str) -> dict:
    """
    从tradingagents代理的输出中提取信号信息，转换为当前系统的格式。
    """
    try:
        # tradingagents的输出通常包含在messages的最后一条消息中
        if "messages" in output and output["messages"]:
            content = output["messages"][-1].content
            
            # 尝试解析为JSON
            if isinstance(content, str):
                try:
                    parsed_content = json.loads(content)
                    content = parsed_content
                except json.JSONDecodeError:
                    pass
            
            # 根据不同的代理类型提取信号
            if agent_name == "tradingagents_fundamentals_analyst":
                return _extract_fundamentals_signal(content, output)
            elif agent_name == "tradingagents_market_analyst":
                return _extract_market_signal(content, output)
            elif agent_name == "tradingagents_news_analyst":
                return _extract_news_signal(content, output)
            elif agent_name == "tradingagents_social_media_analyst":
                return _extract_social_media_signal(content, output)
        
        # 如果无法提取信号，返回中性信号
        return {
            "signal": "neutral",
            "confidence": 50.0,
            "reasoning": {
                "analysis": "Unable to extract signal from tradingagents output",
                "raw_output": str(output)
            }
        }
        
    except Exception as e:
        return {
            "signal": "neutral",
            "confidence": 50.0,
            "reasoning": {
                "error": f"Error processing tradingagents output: {str(e)}",
                "raw_output": str(output)
            }
        }


def _extract_fundamentals_signal(content, full_output: dict) -> dict:
    """从基本面分析师输出中提取信号"""
    # 基本面分析师通常会提供详细的财务分析报告
    # 我们需要从报告中推断信号
    
    if isinstance(content, str):
        content_lower = content.lower()
        
        # 寻找积极和消极的关键词
        positive_keywords = ['strong', 'good', 'positive', 'bullish', 'buy', 'growth', 'profitable', 'healthy']
        negative_keywords = ['weak', 'poor', 'negative', 'bearish', 'sell', 'decline', 'loss', 'risky']
        
        positive_count = sum(1 for keyword in positive_keywords if keyword in content_lower)
        negative_count = sum(1 for keyword in negative_keywords if keyword in content_lower)
        
        if positive_count > negative_count:
            signal = "bullish"
            confidence = min(80.0, 50.0 + (positive_count - negative_count) * 10)
        elif negative_count > positive_count:
            signal = "bearish"
            confidence = min(80.0, 50.0 + (negative_count - positive_count) * 10)
        else:
            signal = "neutral"
            confidence = 50.0
    else:
        signal = "neutral"
        confidence = 50.0
    
    return {
        "signal": signal,
        "confidence": confidence,
        "reasoning": {
            "fundamentals_analysis": content,
            "report_source": "tradingagents_fundamentals_analyst"
        }
    }


def _extract_market_signal(content, full_output: dict) -> dict:
    """从市场分析师输出中提取信号"""
    # 市场分析师通常会提供技术指标分析
    
    if isinstance(content, str):
        content_lower = content.lower()
        
        # 寻找技术分析相关的关键词
        bullish_keywords = ['uptrend', 'bullish', 'buy signal', 'oversold', 'support', 'breakout', 'momentum up']
        bearish_keywords = ['downtrend', 'bearish', 'sell signal', 'overbought', 'resistance', 'breakdown', 'momentum down']
        
        bullish_count = sum(1 for keyword in bullish_keywords if keyword in content_lower)
        bearish_count = sum(1 for keyword in bearish_keywords if keyword in content_lower)
        
        if bullish_count > bearish_count:
            signal = "bullish"
            confidence = min(85.0, 50.0 + (bullish_count - bearish_count) * 12)
        elif bearish_count > bullish_count:
            signal = "bearish"
            confidence = min(85.0, 50.0 + (bearish_count - bullish_count) * 12)
        else:
            signal = "neutral"
            confidence = 50.0
    else:
        signal = "neutral"
        confidence = 50.0
    
    return {
        "signal": signal,
        "confidence": confidence,
        "reasoning": {
            "market_analysis": content,
            "report_source": "tradingagents_market_analyst"
        }
    }


def _extract_news_signal(content, full_output: dict) -> dict:
    """从新闻分析师输出中提取信号"""
    # 新闻分析师会分析新闻情感和影响
    
    if isinstance(content, str):
        content_lower = content.lower()
        
        # 新闻情感关键词
        positive_news_keywords = ['positive', 'good news', 'growth', 'expansion', 'profit', 'success', 'bullish', 'optimistic']
        negative_news_keywords = ['negative', 'bad news', 'decline', 'loss', 'problem', 'concern', 'bearish', 'pessimistic']
        
        positive_count = sum(1 for keyword in positive_news_keywords if keyword in content_lower)
        negative_count = sum(1 for keyword in negative_news_keywords if keyword in content_lower)
        
        if positive_count > negative_count:
            signal = "bullish"
            confidence = min(75.0, 50.0 + (positive_count - negative_count) * 8)
        elif negative_count > positive_count:
            signal = "bearish"
            confidence = min(75.0, 50.0 + (negative_count - positive_count) * 8)
        else:
            signal = "neutral"
            confidence = 50.0
    else:
        signal = "neutral"
        confidence = 50.0
    
    return {
        "signal": signal,
        "confidence": confidence,
        "reasoning": {
            "news_analysis": content,
            "report_source": "tradingagents_news_analyst"
        }
    }


def _extract_social_media_signal(content, full_output: dict) -> dict:
    """从社交媒体分析师输出中提取信号"""
    # 社交媒体分析师会分析社交媒体情感
    
    if isinstance(content, str):
        content_lower = content.lower()
        
        # 社交媒体情感关键词
        positive_sentiment_keywords = ['positive sentiment', 'bullish sentiment', 'optimistic', 'excited', 'confident', 'buy']
        negative_sentiment_keywords = ['negative sentiment', 'bearish sentiment', 'pessimistic', 'worried', 'concerned', 'sell']
        
        positive_count = sum(1 for keyword in positive_sentiment_keywords if keyword in content_lower)
        negative_count = sum(1 for keyword in negative_sentiment_keywords if keyword in content_lower)
        
        if positive_count > negative_count:
            signal = "bullish"
            confidence = min(70.0, 50.0 + (positive_count - negative_count) * 7)
        elif negative_count > positive_count:
            signal = "bearish"
            confidence = min(70.0, 50.0 + (negative_count - positive_count) * 7)
        else:
            signal = "neutral"
            confidence = 50.0
    else:
        signal = "neutral"
        confidence = 50.0
    
    return {
        "signal": signal,
        "confidence": confidence,
        "reasoning": {
            "social_media_analysis": content,
            "report_source": "tradingagents_social_media_analyst"
        }
    }


# 适配器函数 - 将tradingagents代理包装为当前系统的代理格式

def tradingagents_fundamentals_analyst_agent(state: AgentState):
    """TradingAgents基本面分析师的适配器函数"""
    data = state["data"]
    metadata = state["metadata"]
    tickers = data["tickers"]
    start_date = data["start_date"]
    end_date = data["end_date"]

    # 创建toolkit适配器
    toolkit_adapter = TradingAgentsToolkitAdapter(
        metadata["model_name"],
        metadata["model_provider"]
    )

    # 创建tradingagents基本面分析师
    fundamentals_analyst_node = create_fundamentals_analyst(
        toolkit_adapter.llm,
        toolkit_adapter.toolkit
    )

    # 为每个ticker运行分析
    analysis_results = {}

    for ticker in tickers:
        progress.update_status("tradingagents_fundamentals_analyst", ticker, "Running analysis")

        try:
            # 转换状态格式
            tradingagents_state = _convert_tradingagents_state_to_current(ticker, start_date, end_date)

            # 运行tradingagents分析师
            result = fundamentals_analyst_node(tradingagents_state)

            # 提取信号
            signal_data = _extract_signal_from_tradingagents_output(result, "tradingagents_fundamentals_analyst")
            analysis_results[ticker] = signal_data

            progress.update_status("tradingagents_fundamentals_analyst", ticker, "Done")

        except Exception as e:
            progress.update_status("tradingagents_fundamentals_analyst", ticker, f"Error: {str(e)}")
            analysis_results[ticker] = {
                "signal": "neutral",
                "confidence": 50.0,
                "reasoning": {
                    "error": f"Error running tradingagents fundamentals analyst: {str(e)}"
                }
            }

    # 创建消息
    message = HumanMessage(
        content=json.dumps(analysis_results),
        name="tradingagents_fundamentals_analyst",
    )

    # 显示推理过程
    if metadata["show_reasoning"]:
        show_agent_reasoning(analysis_results, "TradingAgents Fundamentals Analyst")

    # 添加到分析师信号中
    data["analyst_signals"]["tradingagents_fundamentals_analyst"] = analysis_results

    progress.update_status("tradingagents_fundamentals_analyst", None, "Done")

    return {
        "messages": [message],
        "data": data,
    }


def tradingagents_market_analyst_agent(state: AgentState):
    """TradingAgents市场分析师的适配器函数"""
    data = state["data"]
    metadata = state["metadata"]
    tickers = data["tickers"]
    start_date = data["start_date"]
    end_date = data["end_date"]

    # 创建toolkit适配器
    toolkit_adapter = TradingAgentsToolkitAdapter(
        metadata["model_name"],
        metadata["model_provider"]
    )

    # 创建tradingagents市场分析师
    market_analyst_node = create_market_analyst(
        toolkit_adapter.llm,
        toolkit_adapter.toolkit
    )

    # 为每个ticker运行分析
    analysis_results = {}

    for ticker in tickers:
        progress.update_status("tradingagents_market_analyst", ticker, "Running analysis")

        try:
            # 转换状态格式
            tradingagents_state = _convert_tradingagents_state_to_current(ticker, start_date, end_date)

            # 运行tradingagents分析师
            result = market_analyst_node(tradingagents_state)

            # 提取信号
            signal_data = _extract_signal_from_tradingagents_output(result, "tradingagents_market_analyst")
            analysis_results[ticker] = signal_data

            progress.update_status("tradingagents_market_analyst", ticker, "Done")

        except Exception as e:
            progress.update_status("tradingagents_market_analyst", ticker, f"Error: {str(e)}")
            analysis_results[ticker] = {
                "signal": "neutral",
                "confidence": 50.0,
                "reasoning": {
                    "error": f"Error running tradingagents market analyst: {str(e)}"
                }
            }

    # 创建消息
    message = HumanMessage(
        content=json.dumps(analysis_results),
        name="tradingagents_market_analyst",
    )

    # 显示推理过程
    if metadata["show_reasoning"]:
        show_agent_reasoning(analysis_results, "TradingAgents Market Analyst")

    # 添加到分析师信号中
    data["analyst_signals"]["tradingagents_market_analyst"] = analysis_results

    progress.update_status("tradingagents_market_analyst", None, "Done")

    return {
        "messages": [message],
        "data": data,
    }


def tradingagents_news_analyst_agent(state: AgentState):
    """TradingAgents新闻分析师的适配器函数"""
    data = state["data"]
    metadata = state["metadata"]
    tickers = data["tickers"]
    start_date = data["start_date"]
    end_date = data["end_date"]

    # 创建toolkit适配器
    toolkit_adapter = TradingAgentsToolkitAdapter(
        metadata["model_name"],
        metadata["model_provider"]
    )

    # 创建tradingagents新闻分析师
    news_analyst_node = create_news_analyst(
        toolkit_adapter.llm,
        toolkit_adapter.toolkit
    )

    # 为每个ticker运行分析
    analysis_results = {}

    for ticker in tickers:
        progress.update_status("tradingagents_news_analyst", ticker, "Running analysis")

        try:
            # 转换状态格式
            tradingagents_state = _convert_tradingagents_state_to_current(ticker, start_date, end_date)

            # 运行tradingagents分析师
            result = news_analyst_node(tradingagents_state)

            # 提取信号
            signal_data = _extract_signal_from_tradingagents_output(result, "tradingagents_news_analyst")
            analysis_results[ticker] = signal_data

            progress.update_status("tradingagents_news_analyst", ticker, "Done")

        except Exception as e:
            progress.update_status("tradingagents_news_analyst", ticker, f"Error: {str(e)}")
            analysis_results[ticker] = {
                "signal": "neutral",
                "confidence": 50.0,
                "reasoning": {
                    "error": f"Error running tradingagents news analyst: {str(e)}"
                }
            }

    # 创建消息
    message = HumanMessage(
        content=json.dumps(analysis_results),
        name="tradingagents_news_analyst",
    )

    # 显示推理过程
    if metadata["show_reasoning"]:
        show_agent_reasoning(analysis_results, "TradingAgents News Analyst")

    # 添加到分析师信号中
    data["analyst_signals"]["tradingagents_news_analyst"] = analysis_results

    progress.update_status("tradingagents_news_analyst", None, "Done")

    return {
        "messages": [message],
        "data": data,
    }


def tradingagents_social_media_analyst_agent(state: AgentState):
    """TradingAgents社交媒体分析师的适配器函数"""
    data = state["data"]
    metadata = state["metadata"]
    tickers = data["tickers"]
    start_date = data["start_date"]
    end_date = data["end_date"]

    # 创建toolkit适配器
    toolkit_adapter = TradingAgentsToolkitAdapter(
        metadata["model_name"],
        metadata["model_provider"]
    )

    # 创建tradingagents社交媒体分析师
    social_media_analyst_node = create_social_media_analyst(
        toolkit_adapter.llm,
        toolkit_adapter.toolkit
    )

    # 为每个ticker运行分析
    analysis_results = {}

    for ticker in tickers:
        progress.update_status("tradingagents_social_media_analyst", ticker, "Running analysis")

        try:
            # 转换状态格式
            tradingagents_state = _convert_tradingagents_state_to_current(ticker, start_date, end_date)

            # 运行tradingagents分析师
            result = social_media_analyst_node(tradingagents_state)

            # 提取信号
            signal_data = _extract_signal_from_tradingagents_output(result, "tradingagents_social_media_analyst")
            analysis_results[ticker] = signal_data

            progress.update_status("tradingagents_social_media_analyst", ticker, "Done")

        except Exception as e:
            progress.update_status("tradingagents_social_media_analyst", ticker, f"Error: {str(e)}")
            analysis_results[ticker] = {
                "signal": "neutral",
                "confidence": 50.0,
                "reasoning": {
                    "error": f"Error running tradingagents social media analyst: {str(e)}"
                }
            }

    # 创建消息
    message = HumanMessage(
        content=json.dumps(analysis_results),
        name="tradingagents_social_media_analyst",
    )

    # 显示推理过程
    if metadata["show_reasoning"]:
        show_agent_reasoning(analysis_results, "TradingAgents Social Media Analyst")

    # 添加到分析师信号中
    data["analyst_signals"]["tradingagents_social_media_analyst"] = analysis_results

    progress.update_status("tradingagents_social_media_analyst", None, "Done")

    return {
        "messages": [message],
        "data": data,
    }
