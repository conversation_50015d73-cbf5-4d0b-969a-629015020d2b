from langchain_core.messages import HumanMessage
from src.graph.state import Agent<PERSON><PERSON>, show_agent_reasoning
from src.utils.progress import progress
from src.tools.api import get_company_news
import pandas as pd
import numpy as np
import json
from datetime import datetime, timedelta


##### News Analyst Agent #####
def news_analyst_agent(state: AgentState):
    """Analyzes news sentiment and trends to generate trading signals for multiple tickers."""
    data = state["data"]
    end_date = data["end_date"]
    tickers = data["tickers"]

    # Initialize news analysis for each ticker
    news_analysis = {}

    for ticker in tickers:
        progress.update_status("news_analyst_agent", ticker, "Fetching company news")

        # Get company news for sentiment analysis
        company_news = get_company_news(
            ticker=ticker,
            end_date=end_date,
            limit=100,  # Get more news for better analysis
            agent_name="news_analyst_agent",
        )

        if not company_news:
            progress.update_status("news_analyst_agent", ticker, "Failed: No news data found")
            continue

        # Initialize signals list for different news aspects
        signals = []
        reasoning = {}

        progress.update_status("news_analyst_agent", ticker, "Analyzing news sentiment")
        
        # 1. Overall Sentiment Analysis
        sentiment_scores = []
        positive_count = 0
        negative_count = 0
        neutral_count = 0
        
        for news in company_news:
            if hasattr(news, 'sentiment') and news.sentiment:
                if news.sentiment.lower() == 'positive':
                    sentiment_scores.append(1)
                    positive_count += 1
                elif news.sentiment.lower() == 'negative':
                    sentiment_scores.append(-1)
                    negative_count += 1
                else:
                    sentiment_scores.append(0)
                    neutral_count += 1
            else:
                sentiment_scores.append(0)
                neutral_count += 1
        
        # Calculate sentiment signal
        if len(sentiment_scores) > 0:
            avg_sentiment = np.mean(sentiment_scores)
            if avg_sentiment > 0.2:
                sentiment_signal = "bullish"
            elif avg_sentiment < -0.2:
                sentiment_signal = "bearish"
            else:
                sentiment_signal = "neutral"
        else:
            sentiment_signal = "neutral"
            avg_sentiment = 0
            
        signals.append(sentiment_signal)
        reasoning["sentiment_signal"] = {
            "signal": sentiment_signal,
            "details": f"Avg Sentiment: {avg_sentiment:.2f}, Positive: {positive_count}, Negative: {negative_count}, Neutral: {neutral_count}",
        }

        progress.update_status("news_analyst_agent", ticker, "Analyzing news frequency")
        
        # 2. News Frequency Analysis
        # Analyze news frequency over time periods
        recent_news = []
        older_news = []
        
        end_datetime = datetime.strptime(end_date, "%Y-%m-%d")
        recent_threshold = end_datetime - timedelta(days=7)  # Last 7 days
        
        for news in company_news:
            if hasattr(news, 'date') and news.date:
                try:
                    news_date = datetime.strptime(news.date, "%Y-%m-%d")
                    if news_date >= recent_threshold:
                        recent_news.append(news)
                    else:
                        older_news.append(news)
                except:
                    older_news.append(news)
            else:
                older_news.append(news)
        
        # Calculate frequency signal
        recent_count = len(recent_news)
        total_count = len(company_news)
        
        if recent_count > 5:  # High news activity
            frequency_signal = "bullish" if sentiment_signal == "bullish" else "bearish"
        elif recent_count < 2:  # Low news activity
            frequency_signal = "neutral"
        else:
            frequency_signal = "neutral"
            
        signals.append(frequency_signal)
        reasoning["frequency_signal"] = {
            "signal": frequency_signal,
            "details": f"Recent News (7d): {recent_count}, Total News: {total_count}",
        }

        progress.update_status("news_analyst_agent", ticker, "Analyzing news impact")
        
        # 3. News Impact Analysis
        # Analyze the potential impact based on news content and sentiment
        high_impact_keywords = [
            'earnings', 'revenue', 'profit', 'loss', 'acquisition', 'merger',
            'partnership', 'contract', 'lawsuit', 'regulation', 'fda', 'approval',
            'breakthrough', 'innovation', 'bankruptcy', 'restructuring'
        ]
        
        impact_score = 0
        high_impact_news = 0
        
        for news in recent_news:
            if hasattr(news, 'title') and news.title:
                title_lower = news.title.lower()
                for keyword in high_impact_keywords:
                    if keyword in title_lower:
                        high_impact_news += 1
                        if hasattr(news, 'sentiment') and news.sentiment:
                            if news.sentiment.lower() == 'positive':
                                impact_score += 1
                            elif news.sentiment.lower() == 'negative':
                                impact_score -= 1
                        break
        
        # Calculate impact signal
        if impact_score > 1:
            impact_signal = "bullish"
        elif impact_score < -1:
            impact_signal = "bearish"
        else:
            impact_signal = "neutral"
            
        signals.append(impact_signal)
        reasoning["impact_signal"] = {
            "signal": impact_signal,
            "details": f"High Impact News: {high_impact_news}, Impact Score: {impact_score}",
        }

        progress.update_status("news_analyst_agent", ticker, "Analyzing news trend")
        
        # 4. News Trend Analysis
        # Compare recent sentiment with older sentiment
        if len(recent_news) > 0 and len(older_news) > 0:
            recent_sentiment = []
            older_sentiment = []
            
            for news in recent_news:
                if hasattr(news, 'sentiment') and news.sentiment:
                    if news.sentiment.lower() == 'positive':
                        recent_sentiment.append(1)
                    elif news.sentiment.lower() == 'negative':
                        recent_sentiment.append(-1)
                    else:
                        recent_sentiment.append(0)
            
            for news in older_news[:len(recent_news)]:  # Compare same number of articles
                if hasattr(news, 'sentiment') and news.sentiment:
                    if news.sentiment.lower() == 'positive':
                        older_sentiment.append(1)
                    elif news.sentiment.lower() == 'negative':
                        older_sentiment.append(-1)
                    else:
                        older_sentiment.append(0)
            
            if len(recent_sentiment) > 0 and len(older_sentiment) > 0:
                recent_avg = np.mean(recent_sentiment)
                older_avg = np.mean(older_sentiment)
                trend_change = recent_avg - older_avg
                
                if trend_change > 0.3:
                    trend_signal = "bullish"
                elif trend_change < -0.3:
                    trend_signal = "bearish"
                else:
                    trend_signal = "neutral"
            else:
                trend_signal = "neutral"
                trend_change = 0
        else:
            trend_signal = "neutral"
            trend_change = 0
            
        signals.append(trend_signal)
        reasoning["trend_signal"] = {
            "signal": trend_signal,
            "details": f"Sentiment Trend Change: {trend_change:.2f}",
        }

        progress.update_status("news_analyst_agent", ticker, "Calculating final signal")
        
        # Determine overall signal
        bullish_signals = signals.count("bullish")
        bearish_signals = signals.count("bearish")

        if bullish_signals > bearish_signals:
            overall_signal = "bullish"
        elif bearish_signals > bullish_signals:
            overall_signal = "bearish"
        else:
            overall_signal = "neutral"

        # Calculate confidence level
        total_signals = len(signals)
        confidence = round(max(bullish_signals, bearish_signals) / total_signals, 2) * 100

        news_analysis[ticker] = {
            "signal": overall_signal,
            "confidence": confidence,
            "reasoning": reasoning,
        }

        progress.update_status("news_analyst_agent", ticker, "Done")

    # Create the news analysis message
    message = HumanMessage(
        content=json.dumps(news_analysis),
        name="news_analyst_agent",
    )

    # Print the reasoning if the flag is set
    if state["metadata"]["show_reasoning"]:
        show_agent_reasoning(news_analysis, "News Analysis Agent")

    # Add the signal to the analyst_signals list
    state["data"]["analyst_signals"]["news_analyst_agent"] = news_analysis

    progress.update_status("news_analyst_agent", None, "Done")
    
    return {
        "messages": [message],
        "data": data,
    }
