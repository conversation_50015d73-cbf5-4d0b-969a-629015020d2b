from langchain_core.messages import HumanMessage
from src.graph.state import Agent<PERSON><PERSON>, show_agent_reasoning
from src.utils.progress import progress
from src.tools.api import get_price_data, get_financial_metrics
import pandas as pd
import numpy as np
import json


##### Market Analyst Agent #####
def market_analyst_agent(state: AgentState):
    """Analyzes market and technical indicators to generate trading signals for multiple tickers."""
    data = state["data"]
    end_date = data["end_date"]
    tickers = data["tickers"]

    # Initialize market analysis for each ticker
    market_analysis = {}

    for ticker in tickers:
        progress.update_status("market_analyst_agent", ticker, "Fetching price data")

        # Get price data for technical analysis
        price_data = get_price_data(
            ticker=ticker,
            end_date=end_date,
            period="1y",  # Get 1 year of data for technical indicators
            agent_name="market_analyst_agent",
        )

        if not price_data or len(price_data) < 50:
            progress.update_status("market_analyst_agent", ticker, "Failed: Insufficient price data")
            continue

        # Convert to DataFrame for analysis
        df = pd.DataFrame([{
            'date': p.date,
            'open': p.open,
            'high': p.high,
            'low': p.low,
            'close': p.close,
            'volume': p.volume
        } for p in price_data])
        
        df = df.sort_values('date').reset_index(drop=True)
        
        # Calculate technical indicators
        signals = []
        reasoning = {}

        progress.update_status("market_analyst_agent", ticker, "Calculating moving averages")
        
        # 1. Moving Average Analysis
        df['sma_20'] = df['close'].rolling(window=20).mean()
        df['sma_50'] = df['close'].rolling(window=50).mean()
        df['ema_10'] = df['close'].ewm(span=10).mean()
        
        current_price = df['close'].iloc[-1]
        sma_20 = df['sma_20'].iloc[-1]
        sma_50 = df['sma_50'].iloc[-1]
        ema_10 = df['ema_10'].iloc[-1]
        
        # Moving average signals
        ma_score = 0
        if current_price > sma_20:
            ma_score += 1
        if current_price > sma_50:
            ma_score += 1
        if sma_20 > sma_50:
            ma_score += 1
        if current_price > ema_10:
            ma_score += 1
            
        signals.append("bullish" if ma_score >= 3 else "bearish" if ma_score <= 1 else "neutral")
        reasoning["moving_average_signal"] = {
            "signal": signals[0],
            "details": f"Price: ${current_price:.2f}, SMA20: ${sma_20:.2f}, SMA50: ${sma_50:.2f}, EMA10: ${ema_10:.2f}",
        }

        progress.update_status("market_analyst_agent", ticker, "Calculating momentum indicators")
        
        # 2. RSI Analysis
        try:
            # Calculate RSI manually
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            current_rsi = rsi.iloc[-1] if not pd.isna(rsi.iloc[-1]) else 50

            if current_rsi < 30:
                rsi_signal = "bullish"  # Oversold
            elif current_rsi > 70:
                rsi_signal = "bearish"  # Overbought
            else:
                rsi_signal = "neutral"

            signals.append(rsi_signal)
            reasoning["rsi_signal"] = {
                "signal": rsi_signal,
                "details": f"RSI: {current_rsi:.2f}",
            }
        except:
            signals.append("neutral")
            reasoning["rsi_signal"] = {
                "signal": "neutral",
                "details": "RSI calculation failed",
            }

        progress.update_status("market_analyst_agent", ticker, "Calculating MACD")
        
        # 3. MACD Analysis
        try:
            # Calculate MACD manually
            ema_12 = df['close'].ewm(span=12).mean()
            ema_26 = df['close'].ewm(span=26).mean()
            macd_line = ema_12 - ema_26
            macd_signal_line = macd_line.ewm(span=9).mean()
            macd_histogram = macd_line - macd_signal_line

            current_macd = macd_line.iloc[-1] if not pd.isna(macd_line.iloc[-1]) else 0
            current_signal = macd_signal_line.iloc[-1] if not pd.isna(macd_signal_line.iloc[-1]) else 0
            current_hist = macd_histogram.iloc[-1] if not pd.isna(macd_histogram.iloc[-1]) else 0

            if current_macd > current_signal and current_hist > 0:
                macd_signal_result = "bullish"
            elif current_macd < current_signal and current_hist < 0:
                macd_signal_result = "bearish"
            else:
                macd_signal_result = "neutral"

            signals.append(macd_signal_result)
            reasoning["macd_signal"] = {
                "signal": macd_signal_result,
                "details": f"MACD: {current_macd:.4f}, Signal: {current_signal:.4f}, Histogram: {current_hist:.4f}",
            }
        except:
            signals.append("neutral")
            reasoning["macd_signal"] = {
                "signal": "neutral",
                "details": "MACD calculation failed",
            }

        progress.update_status("market_analyst_agent", ticker, "Analyzing volume trends")
        
        # 4. Volume Analysis
        avg_volume = df['volume'].rolling(window=20).mean().iloc[-1]
        current_volume = df['volume'].iloc[-1]
        volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1
        
        # Price change analysis
        price_change = (current_price - df['close'].iloc[-2]) / df['close'].iloc[-2] if len(df) > 1 else 0
        
        if volume_ratio > 1.5 and price_change > 0:
            volume_signal = "bullish"  # High volume with price increase
        elif volume_ratio > 1.5 and price_change < 0:
            volume_signal = "bearish"  # High volume with price decrease
        else:
            volume_signal = "neutral"
            
        signals.append(volume_signal)
        reasoning["volume_signal"] = {
            "signal": volume_signal,
            "details": f"Volume Ratio: {volume_ratio:.2f}, Price Change: {price_change:.2%}",
        }

        progress.update_status("market_analyst_agent", ticker, "Calculating final signal")
        
        # Determine overall signal
        bullish_signals = signals.count("bullish")
        bearish_signals = signals.count("bearish")

        if bullish_signals > bearish_signals:
            overall_signal = "bullish"
        elif bearish_signals > bullish_signals:
            overall_signal = "bearish"
        else:
            overall_signal = "neutral"

        # Calculate confidence level
        total_signals = len(signals)
        confidence = round(max(bullish_signals, bearish_signals) / total_signals, 2) * 100

        market_analysis[ticker] = {
            "signal": overall_signal,
            "confidence": confidence,
            "reasoning": reasoning,
        }

        progress.update_status("market_analyst_agent", ticker, "Done")

    # Create the market analysis message
    message = HumanMessage(
        content=json.dumps(market_analysis),
        name="market_analyst_agent",
    )

    # Print the reasoning if the flag is set
    if state["metadata"]["show_reasoning"]:
        show_agent_reasoning(market_analysis, "Market Analysis Agent")

    # Add the signal to the analyst_signals list
    state["data"]["analyst_signals"]["market_analyst_agent"] = market_analysis

    progress.update_status("market_analyst_agent", None, "Done")
    
    return {
        "messages": [message],
        "data": data,
    }
